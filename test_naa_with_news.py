#!/usr/bin/env python3
"""
测试NAA代理处理新闻数据
"""

import sys
import os
sys.path.append('.')

from stock_trading_env import StockTradingEnv
from agents.analyst_agents import NewsAnalystAgent
from contribution_assessment.llm_interface import LLMInterface
import json

def test_naa_with_news():
    """测试NAA代理处理新闻数据"""
    
    # 创建基本配置
    config = {
        "fail_on_large_gaps": False,
        "fill_date_gaps": False,
        "max_date_gap_days": 7
    }
    
    # 创建环境实例并加载数据
    env = StockTradingEnv(config)
    stocks = ['AAPL', 'MSFT']
    env.load_data('2025-01-01', '2025-01-01', stocks)

    # 重置环境以确保正确初始化
    env.reset()

    # 获取环境状态
    state = env.get_state()
    
    print("=== 环境状态检查 ===")
    print(f"当前日期: {state.get('current_date')}")
    print(f"新闻历史数据: {len(state.get('news_history', {}))}")
    
    # 显示新闻数据详情
    news_history = state.get('news_history', {})
    for date_key, stocks_news in news_history.items():
        print(f"{date_key}: {sum(len(news_list) for news_list in stocks_news.values())} 条新闻")
        for stock, news_list in stocks_news.items():
            if news_list:
                print(f"  {stock}: {len(news_list)} 条")
                print(f"    示例: {news_list[0].get('title', '无标题')[:60]}...")
    
    # 创建NAA代理（不使用LLM，测试数据传递）
    naa_agent = NewsAnalystAgent()
    
    print("\n=== NAA代理处理测试 ===")
    
    # 测试代理的状态格式化功能
    formatted_state = naa_agent.format_state_for_llm(state)
    print("格式化的状态信息:")
    print(formatted_state[:500] + "..." if len(formatted_state) > 500 else formatted_state)
    
    # 测试代理处理（不使用LLM）
    try:
        result = naa_agent.process(state)
        print(f"\nNAA代理处理结果: {result}")
        
        if result.get('llm_used') == False:
            print("✅ NAA代理成功处理了状态数据（使用默认输出）")
        else:
            print("✅ NAA代理成功处理了状态数据（使用LLM）")
            
    except Exception as e:
        print(f"❌ NAA代理处理失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_naa_with_news()

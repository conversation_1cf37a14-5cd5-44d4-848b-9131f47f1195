"""
美股交易环境模拟器

提供美股交易环境的模拟，包括价格数据、交易执行、收益计算等功能
"""
import os
import numpy as np
import pandas as pd
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Tuple, Optional
import glob

class StockTradingEnv:
    """美股交易环境类"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化交易环境
        
        参数:
            config: 环境配置，包含起始日期、结束日期、初始资金等
        """
        self.config = config
        self.starting_cash = config.get("starting_cash", 1_000_000)  # 默认100万美元
        self.trading_fee_rate = config.get("trading_fee_rate", 0.001)  # 交易费率0.1%
        self.price_window = config.get("price_window", 20)  # 价格历史窗口
        self.news_window = config.get("news_window", 7)  # 新闻历史窗口
        self.current_day_index = 0
        
        # 加载股票数据
        self.load_data(
            config.get("start_date", "2023-01-01"),
            config.get("end_date", "2023-12-31"),
            config.get("stocks", ["AAPL", "MSFT", "AMZN", "GOOGL", "META"])
        )
        
        # 初始化投资组合
        self.reset()
        
    def load_data(self, start_date: str, end_date: str, stocks: List[str]) -> None:
        """
        加载股票数据
        
        参数:
            start_date: 起始日期
            end_date: 结束日期
            stocks: 股票代码列表
        """
        self.stocks = stocks
        self.start_date = start_date
        self.end_date = end_date
        
        # 加载真实数据或生成模拟数据
        self.price_data = self._load_price_data_and_get_trading_days(start_date, end_date, stocks)
        self.news_data = self._load_news_data(stocks)
        self.fundamental_data = self._load_fundamental_data(stocks)
        
    def _load_price_data_and_get_trading_days(self, start_date: str, end_date: str, stocks: List[str]) -> Dict[str, pd.DataFrame]:
        """
        加载价格数据并从中提取实际的交易日
        
        优先从SQLite数据库加载，如果不存在则尝试从JSON文件加载
        """
        price_data = {}
        all_trading_days = set()  # 用于收集所有股票的交易日
        
        for stock in stocks:
            stock_data = []
            
            # 首先尝试从SQLite数据库加载
            # 获取项目根目录路径
            project_root = os.path.dirname(os.path.abspath(__file__))
            db_path = os.path.join(project_root, "data", "tickers", stock, f"{stock}_data.db")
            if os.path.exists(db_path):
                try:
                    import sqlite3
                    conn = sqlite3.connect(db_path)
                    
                    # 查询OHLCV数据
                    query = """
                    SELECT ticker, trade_date, Open, High, Low, Close, Adj_Close, Volume
                    FROM ohlcv 
                    WHERE ticker = ? AND trade_date BETWEEN ? AND ?
                    ORDER BY trade_date ASC
                    """
                    
                    cursor = conn.cursor()
                    cursor.execute(query, (stock, start_date, end_date))
                    rows = cursor.fetchall()
                    
                    if rows:
                        # 转换为DataFrame
                        df = pd.DataFrame(rows, columns=['ticker', 'date', 'open', 'high', 'low', 'close', 'adj_close', 'volume'])
                        df['date'] = pd.to_datetime(df['date'])
                        df = df.drop('ticker', axis=1)  # 移除ticker列
                        price_data[stock] = df
                        
                        # 收集这个股票的交易日
                        stock_trading_days = set(df['date'].dt.strftime('%Y-%m-%d').tolist())
                        all_trading_days.update(stock_trading_days)
                        
                        # 数据加载信息已记录在日志中
                        conn.close()
                        continue
                    else:
                        # 数据库查询结果已记录在日志中
                        # 尝试查询数据库中所有可用的日期范围
                        cursor.execute("SELECT MIN(trade_date), MAX(trade_date) FROM ohlcv WHERE ticker = ?", (stock,))
                        date_range_result = cursor.fetchone()
                        if date_range_result and date_range_result[0]:
                            # 数据库日期范围信息已记录在日志中
                            pass
                        conn.close()
                        # 为这个股票创建空的DataFrame
                        price_data[stock] = pd.DataFrame()
                        
                except Exception as e:
                    # 数据库加载错误已记录在日志中
                    if 'conn' in locals():
                        conn.close()
                    # 为这个股票创建空的DataFrame
                    price_data[stock] = pd.DataFrame()
            
            else:
                # 如果数据库不存在，尝试从JSON文件加载
                # 由于我们不知道实际的交易日，这里使用pandas工作日作为fallback
                date_range_fallback = pd.date_range(start=start_date, end=end_date, freq='B')
                for date in date_range_fallback:
                    date_str = date.strftime('%Y-%m-%d')
                    date_key = date_str.replace('-', '_')  # 转换为 YYYY_MM_DD 格式
                    # 获取项目根目录路径
                    project_root = os.path.dirname(os.path.abspath(__file__))
                    file_path = os.path.join(project_root, "data", "ohlcv", f"{stock}_price_{date_key}.json")
                    
                    if os.path.exists(file_path):
                        try:
                            with open(file_path, 'r') as f:
                                daily_data = json.load(f)
                                stock_data.append(daily_data)
                                all_trading_days.add(date_str)
                        except Exception as e:
                            # 文件加载错误已记录在日志中
                            pass
                
                # 如果找到了JSON数据，将其转换为 DataFrame
                if stock_data:
                    df = pd.DataFrame(stock_data)
                    # 确保日期列是 datetime 类型
                    df['date'] = pd.to_datetime(df['date'])
                    price_data[stock] = df
                    # JSON数据加载信息已记录在日志中
                else:
                    # 如果没有找到任何数据，使用空的DataFrame
                    pass
                    price_data[stock] = pd.DataFrame()
        
        # 关键修复：从实际数据中确定交易日，并检查连续性
        if all_trading_days:
            # 将交易日转换为排序的日期列表
            sorted_trading_days = sorted(list(all_trading_days))
            
            # 新增：检查日期间隙并报告
            gaps = self._check_and_report_date_gaps(sorted_trading_days)
            
            # 可选：根据配置决定如何处理间隙
            if self.config.get("fail_on_large_gaps", True) and gaps:
                large_gaps = [gap for gap in gaps if gap[2] > self.config.get("max_date_gap_days", 7)]
                if large_gaps:
                    raise ValueError(f"发现 {len(large_gaps)} 个大的日期间隙，这可能导致交易环境出现日期跳跃问题！请检查数据完整性。")
            
            # 可选：填补小间隙（如果配置启用）
            if self.config.get("fill_date_gaps", False):
                sorted_trading_days = self._fill_small_date_gaps(sorted_trading_days)
                # 日期间隙填补信息已记录在日志中
            
            self.trading_days = [pd.Timestamp(date) for date in sorted_trading_days]
            self.trading_days_str = sorted_trading_days  # 保存字符串格式的交易日列表
            self.total_days = len(self.trading_days)
            # 交易日确定信息已记录在日志中
        else:
            # 如果没有任何数据，使用pandas工作日作为fallback
            pass
            date_range = pd.date_range(start=start_date, end=end_date, freq='B')
            self.trading_days = date_range.tolist()
            self.trading_days_str = [date.strftime('%Y-%m-%d') for date in date_range]  # 保存字符串格式
            self.total_days = len(self.trading_days)
                
        return price_data

    def get_current_date_str(self) -> str:
        """获取当前日期的字符串格式"""
        if hasattr(self, 'current_date') and self.current_date is not None:
            if isinstance(self.current_date, pd.Timestamp):
                return self.current_date.strftime('%Y-%m-%d')
            else:
                return str(self.current_date)
        elif hasattr(self, 'current_day_index') and hasattr(self, 'trading_days_str'):
            if 0 <= self.current_day_index < len(self.trading_days_str):
                return self.trading_days_str[self.current_day_index]
        return ""

    def get_trading_days_str(self) -> List[str]:
        """获取字符串格式的交易日列表"""
        if hasattr(self, 'trading_days_str'):
            return self.trading_days_str
        elif hasattr(self, 'trading_days'):
            return [date.strftime('%Y-%m-%d') if isinstance(date, pd.Timestamp) else str(date)
                   for date in self.trading_days]
        return []
    
    def _check_and_report_date_gaps(self, trading_days: List[str]) -> List[Tuple[str, str, int, int, int]]:
        """检查并报告交易日间隙"""
        gaps = []
        for i in range(1, len(trading_days)):
            prev_date = pd.to_datetime(trading_days[i-1])
            curr_date = pd.to_datetime(trading_days[i])
            days_diff = (curr_date - prev_date).days
            
            if days_diff > 7:  # 超过一周的间隙
                gaps.append((trading_days[i-1], trading_days[i], days_diff, i-1, i))
        
        if gaps:
            # 日期间隙检查结果已记录在日志中
            pass
        else:
            # 交易日连续性检查结果已记录在日志中
            pass
        
        return gaps
    
    def _fill_small_date_gaps(self, trading_days: List[str], max_gap_days: int = None) -> List[str]:
        """填补小的日期间隙"""
        if max_gap_days is None:
            max_gap_days = self.config.get("max_date_gap_days", 7)
            
        filled_days = []
        
        for i, date_str in enumerate(trading_days):
            filled_days.append(date_str)
            
            # 检查与下一个日期的间隙
            if i < len(trading_days) - 1:
                curr_date = pd.to_datetime(date_str)
                next_date = pd.to_datetime(trading_days[i + 1])
                days_diff = (next_date - curr_date).days
                
                # 如果间隙小于阈值，填补工作日
                if 1 < days_diff <= max_gap_days:
                    date_range = pd.date_range(
                        start=curr_date + pd.Timedelta(days=1),
                        end=next_date - pd.Timedelta(days=1),
                        freq='B'  # 只包含工作日
                    )
                    for fill_date in date_range:
                        filled_days.append(fill_date.strftime('%Y-%m-%d'))
        
        return sorted(filled_days)

    def _validate_price_data(self, df: pd.DataFrame, stock: str) -> bool:
        """
        验证价格数据的质量

        参数:
            df: 价格数据DataFrame
            stock: 股票代码

        返回:
            数据是否有效
        """
        if df.empty:
            # 数据为空的错误已记录在日志中
            return False

        # 检查必要的列
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            # 缺少必要列的错误已记录在日志中
            return False

        # 检查数据完整性
        null_counts = df[required_columns].isnull().sum()
        if null_counts.any():
            # 数据空值警告已记录在日志中
            pass

        # 检查价格逻辑性
        invalid_prices = (df['high'] < df['low']) | (df['close'] < 0) | (df['volume'] < 0)
        if invalid_prices.any():
            # 无效价格数据错误已记录在日志中
            return False

        # 检查极端价格变动
        if len(df) > 1:
            price_changes = df['close'].pct_change().abs()
            extreme_changes = price_changes > 0.5  # 50%以上的变动
            if extreme_changes.any():
                # 极端价格变动警告已记录在日志中
                pass

        # 数据质量检查通过
        return True

    def _should_adjust_date_range(self, req_start: str, req_end: str,
                                 avail_start: str, avail_end: str) -> bool:
        """
        检查是否应该调整日期范围以匹配可用数据

        参数:
            req_start: 请求的开始日期
            req_end: 请求的结束日期
            avail_start: 可用数据的开始日期
            avail_end: 可用数据的结束日期

        返回:
            是否应该调整日期范围
        """
        from datetime import datetime, timedelta

        req_start_dt = datetime.strptime(req_start, '%Y-%m-%d')
        req_end_dt = datetime.strptime(req_end, '%Y-%m-%d')
        avail_start_dt = datetime.strptime(avail_start, '%Y-%m-%d')
        avail_end_dt = datetime.strptime(avail_end, '%Y-%m-%d')

        # 如果请求范围与可用范围有重叠，且重叠部分足够大，则建议调整
        overlap_start = max(req_start_dt, avail_start_dt)
        overlap_end = min(req_end_dt, avail_end_dt)

        if overlap_start <= overlap_end:
            overlap_days = (overlap_end - overlap_start).days
            req_days = (req_end_dt - req_start_dt).days

            # 如果重叠部分超过请求范围的50%，建议调整
            if overlap_days >= req_days * 0.5:
                print(f"💡 建议调整日期范围到重叠部分: {overlap_start.strftime('%Y-%m-%d')} 到 {overlap_end.strftime('%Y-%m-%d')}")
                return True

        return False

    def _download_price_data(self, stock: str, start_date: str, end_date: str) -> None:
        """
        下载价格数据
        
        参数:
            stock: 股票代码
            start_date: 开始日期
            end_date: 结束日期
        """
        try:
            import subprocess
            script_path = os.path.join(os.path.dirname(__file__), "data", "get_OHLCV_data.py")
            if os.path.exists(script_path):
                print(f"正在下载 {stock} 的价格数据...")
                result = subprocess.run([
                    "python", script_path, stock, start_date, end_date
                ], capture_output=True, text=True, timeout=60)
                
                if result.returncode == 0:
                    print(f"{stock} 价格数据下载成功")
                else:
                    print(f"{stock} 价格数据下载失败: {result.stderr}")
            else:
                print(f"未找到价格数据下载脚本: {script_path}")
        except Exception as e:
            print(f"下载 {stock} 价格数据时出错: {e}")
        
    def _generate_simulated_price_data(self, stock: str, date_range: pd.DatetimeIndex) -> pd.DataFrame:
        """生成模拟价格数据"""
        # 使用股票代码作为随机种子
        # np.random.seed(hash(stock) % 10000) # 移除模拟数据生成逻辑
        
        # 起始价格在50-500之间
        # start_price = np.random.uniform(50, 500) # 移除模拟数据生成逻辑
        
        # 生成每日回报率，均值为0.0002（约5%年化），标准差为0.015（约24%年化波动率）
        # daily_returns = np.random.normal(0.0002, 0.015, size=len(date_range)) # 移除模拟数据生成逻辑
        
        # 计算价格序列
        # prices = [start_price] # 移除模拟数据生成逻辑
        # for ret in daily_returns: # 移除模拟数据生成逻辑
        #     prices.append(prices[-1] * (1 + ret)) # 移除模拟数据生成逻辑
            
        # 创建价格DataFrame
        # df = pd.DataFrame({ # 移除模拟数据生成逻辑
        #     'date': date_range, # 移除模拟数据生成逻辑
        #     'open': prices[:-1], # 移除模拟数据生成逻辑
        #     'close': prices[1:], # 移除模拟数据生成逻辑
        #     'high': [p * (1 + np.random.uniform(0, 0.01)) for p in prices[1:]], # 移除模拟数据生成逻辑
        #     'low': [p * (1 - np.random.uniform(0, 0.01)) for p in prices[1:]], # 移除模拟数据生成逻辑
        #     'volume': np.random.randint(100000, 10000000, size=len(date_range)) # 移除模拟数据生成逻辑
        # }) # 移除模拟数据生成逻辑
        
        # 计算技术指标
        # SMA # 移除模拟数据生成逻辑
        # for period in [5, 10, 20, 50, 200]: # 移除模拟数据生成逻辑
        #     df[f'SMA_{period}'] = df['close'].rolling(window=period).mean() # 移除模拟数据生成逻辑
            
        # MACD # 移除模拟数据生成逻辑
        # df['EMA_12'] = df['close'].ewm(span=12, adjust=False).mean() # 移除模拟数据生成逻辑
        # df['EMA_26'] = df['close'].ewm(span=26, adjust=False).mean() # 移除模拟数据生成逻辑
        # df['MACD'] = df['EMA_12'] - df['EMA_26'] # 移除模拟数据生成逻辑
        # df['Signal_Line'] = df['MACD'].ewm(span=9, adjust=False).mean() # 移除模拟数据生成逻辑
        
        # RSI # 移除模拟数据生成逻辑
        # delta = df['close'].diff() # 移除模拟数据生成逻辑
        # gain = (delta.where(delta > 0, 0)).rolling(window=14).mean() # 移除模拟数据生成逻辑
        # loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean() # 移除模拟数据生成逻辑
        # rs = gain / loss # 移除模拟数据生成逻辑
        # df['RSI'] = 100 - (100 / (1 + rs)) # 移除模拟数据生成逻辑
        
        # return df # 移除模拟数据生成逻辑
        pass # 删除模拟数据生成函数体
        
    def _load_news_data(self, stocks: List[str]) -> Dict[str, Dict[str, List[Dict[str, Any]]]]:
        """
        加载新闻数据
        
        首先尝试从SQLite数据库加载，然后尝试从按天存储的JSON文件加载
        """
        news_data = {}
        
        # 初始化每个日期的新闻数据字典
        for date in self.trading_days:
            date_str = date.strftime('%Y-%m-%d')
            news_data[date_str] = {stock: [] for stock in stocks}
        
        # 首先尝试从SQLite数据库加载新闻数据
        for stock in stocks:
            # 获取项目根目录路径
            project_root = os.path.dirname(os.path.abspath(__file__))
            db_path = os.path.join(project_root, "data", "tickers", stock, f"{stock}_data.db")
            if os.path.exists(db_path):
                try:
                    import sqlite3
                    conn = sqlite3.connect(db_path)
                    cursor = conn.cursor()
                    
                    # 检查是否存在新闻表
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='news'")
                    if cursor.fetchone():
                        # 查询指定日期范围内的新闻
                        start_date_str = self.trading_days[0].strftime('%Y-%m-%d')
                        end_date_str = self.trading_days[-1].strftime('%Y-%m-%d')
                        
                        # 修复SQL查询以处理多种时间戳格式
                        cursor.execute("""
                            SELECT ticker, time_published, title, summary, overall_sentiment_label, url
                            FROM news 
                            WHERE ticker = ? 
                            AND (
                                DATE(time_published) BETWEEN ? AND ?
                                OR SUBSTR(time_published, 1, 4) || '-' || SUBSTR(time_published, 5, 2) || '-' || SUBSTR(time_published, 7, 2) BETWEEN ? AND ?
                            )
                            ORDER BY time_published ASC
                        """, (stock, start_date_str, end_date_str, start_date_str, end_date_str))
                        
                        news_rows = cursor.fetchall()
                        
                        # 按日期组织新闻数据
                        for row in news_rows:
                            _, time_str, title, summary, sentiment, url = row

                            # 处理多种时间戳格式
                            try:
                                if len(time_str) >= 8 and 'T' in time_str and '-' not in time_str[:10]:
                                    # 自定义格式: 20250131T213650
                                    date_part = time_str[:8]  # 20250131
                                    news_date = f"{date_part[:4]}-{date_part[4:6]}-{date_part[6:8]}"  # 2025-01-31
                                else:
                                    # 标准格式: 2025-01-01 18:00:19 或其他格式
                                    news_date = time_str[:10]
                            except Exception as e:
                                print(f"⚠️ 解析时间戳 {time_str} 时出错: {e}")
                                continue

                            # 修复：处理非交易日的新闻数据
                            target_date = news_date
                            if news_date not in news_data:
                                # 如果新闻日期不是交易日，找到最近的下一个交易日
                                target_date = self._find_next_trading_day(news_date)
                                if target_date is None:
                                    # 如果找不到下一个交易日，尝试找前一个交易日
                                    target_date = self._find_previous_trading_day(news_date)

                            if target_date and target_date in news_data:
                                news_item = {
                                    'time': time_str,
                                    'title': title,
                                    'content': summary,
                                    'sentiment': sentiment,
                                    'url': url,
                                    'original_date': news_date  # 保存原始日期信息
                                }
                                news_data[target_date][stock].append(news_item)
                        
                        if news_rows:
                            print(f"从SQLite数据库加载了 {stock} 的 {len(news_rows)} 条新闻数据")
                    
                    conn.close()
                except Exception as e:
                    print(f"从SQLite数据库加载 {stock} 新闻数据时出错: {e}")
                    if 'conn' in locals():
                        conn.close()
        
        # 然后尝试从JSON文件加载新闻数据（作为补充）
        # 修改：支持从data/news目录加载alpha_news格式的新闻文件
        for date in self.trading_days:
            date_str = date.strftime('%Y-%m-%d')

            # 尝试加载对应日期的alpha_news文件
            project_root = os.path.dirname(os.path.abspath(__file__))
            alpha_news_file_path = os.path.join(project_root, "data", "news", f"alpha_news_{date_str}.json")

            if os.path.exists(alpha_news_file_path):
                try:
                    with open(alpha_news_file_path, 'r', encoding='utf-8') as f:
                        alpha_news_data = json.load(f)

                    # 处理alpha_news格式的数据，按股票分类
                    for news_item in alpha_news_data:
                        # 从新闻标题或内容中提取相关股票
                        relevant_stocks = self._extract_stocks_from_news(news_item, stocks)

                        # 将新闻分配给相关股票
                        for stock in relevant_stocks:
                            if stock in stocks:  # 确保股票在我们的关注列表中
                                formatted_news = {
                                    'time': news_item.get('time_published', ''),
                                    'title': news_item.get('title', ''),
                                    'content': news_item.get('summary', ''),
                                    'sentiment': news_item.get('overall_sentiment_label', 'Neutral'),
                                    'sentiment_score': news_item.get('overall_sentiment_score', 0.0),
                                    'url': news_item.get('url', ''),
                                    'source': 'alpha_news'
                                }
                                news_data[date_str][stock].append(formatted_news)

                    # 统计加载的新闻数量
                    total_news_for_date = sum(len(news_data[date_str][stock]) for stock in stocks)
                    if total_news_for_date > 0:
                        print(f"从alpha_news文件加载了 {date_str} 的 {total_news_for_date} 条新闻数据")

                except Exception as e:
                    print(f"加载 {alpha_news_file_path} 出错: {e}")

            # 保留原有的股票特定新闻文件加载逻辑（作为备用）
            for stock in stocks:
                # 检查该日期该股票还没有新闻数据
                if not news_data[date_str][stock]:
                    # 尝试加载对应日期的股票特定新闻文件
                    stock_news_file_path = os.path.join(project_root, "data", "news", f"{stock}_news_{date_str.replace('-', '_')}.json")
                    if os.path.exists(stock_news_file_path):
                        try:
                            with open(stock_news_file_path, 'r', encoding='utf-8') as f:
                                stock_news = json.load(f)
                                news_data[date_str][stock] = stock_news
                                print(f"从股票特定JSON文件补充加载了 {stock} 在 {date_str} 的新闻数据")
                        except Exception as e:
                            print(f"加载 {stock_news_file_path} 出错: {e}")
        
        # 检查是否有新闻数据
        has_real_data = False
        total_news_count = 0
        for date_str, stocks_news in news_data.items():
            for stock, news_list in stocks_news.items():
                if news_list:
                    has_real_data = True
                    total_news_count += len(news_list)
        
        if has_real_data:
            print(f"总共加载了 {total_news_count} 条新闻数据")
        else:
            print("⚠️ 未找到任何新闻数据")
            # 即使没有真实数据，也确保返回一个有效的结构，以防止智能体完全失败
            # 为每个日期和股票提供空的新闻列表，而不是返回空字典
            if not news_data:
                for date in self.trading_days:
                    date_str = date.strftime('%Y-%m-%d')
                    news_data[date_str] = {stock: [] for stock in stocks}
            
        return news_data

    def _extract_stocks_from_news(self, news_item: Dict[str, Any], target_stocks: List[str]) -> List[str]:
        """
        从新闻项中提取相关的股票代码

        参数:
            news_item: 新闻项字典
            target_stocks: 目标股票列表

        返回:
            相关股票代码列表
        """
        relevant_stocks = []

        # 获取新闻标题和摘要
        title = news_item.get('title', '').upper()
        summary = news_item.get('summary', '').upper()

        # 检查每个目标股票是否在新闻中被提及
        for stock in target_stocks:
            stock_upper = stock.upper()

            # 检查股票代码是否直接出现在标题或摘要中
            if (stock_upper in title or stock_upper in summary):
                relevant_stocks.append(stock)
                continue

            # 检查常见的股票名称映射
            stock_name_mappings = {
                'AAPL': ['APPLE', 'APPLE INC'],
                'MSFT': ['MICROSOFT', 'MICROSOFT CORP'],
                'GOOGL': ['GOOGLE', 'ALPHABET', 'ALPHABET INC'],
                'AMZN': ['AMAZON', 'AMAZON.COM'],
                'TSLA': ['TESLA', 'TESLA INC'],
                'META': ['META', 'FACEBOOK', 'META PLATFORMS'],
                'NVDA': ['NVIDIA', 'NVIDIA CORP'],
                'NFLX': ['NETFLIX', 'NETFLIX INC'],
                'CRM': ['SALESFORCE', 'SALESFORCE.COM'],
                'ORCL': ['ORACLE', 'ORACLE CORP']
            }

            # 检查股票名称是否在新闻中
            if stock_upper in stock_name_mappings:
                for name in stock_name_mappings[stock_upper]:
                    if name in title or name in summary:
                        relevant_stocks.append(stock)
                        break

        # 如果没有找到特定股票，但新闻涉及市场整体，则分配给所有股票
        market_keywords = ['S&P 500', 'NASDAQ', 'DOW JONES', 'MARKET', 'STOCKS', 'EQUITY']
        if not relevant_stocks:
            for keyword in market_keywords:
                if keyword in title or keyword in summary:
                    # 对于市场整体新闻，分配给所有目标股票
                    relevant_stocks = target_stocks.copy()
                    break

        # 如果仍然没有找到相关股票，但新闻明确提到了某些股票代码，则尝试模糊匹配
        if not relevant_stocks:
            import re
            # 查找所有可能的股票代码模式 (3-5个大写字母)
            stock_pattern = r'\b[A-Z]{3,5}\b'
            found_tickers = re.findall(stock_pattern, title + ' ' + summary)

            for ticker in found_tickers:
                if ticker in target_stocks:
                    relevant_stocks.append(ticker)

        return list(set(relevant_stocks))  # 去重

    def _find_next_trading_day(self, date_str: str) -> str:
        """
        找到指定日期之后的下一个交易日

        Args:
            date_str: 日期字符串 (YYYY-MM-DD)

        Returns:
            下一个交易日的字符串，如果找不到则返回None
        """
        try:
            target_date = pd.Timestamp(date_str)
            for trading_day in self.trading_days:
                if trading_day > target_date:
                    return trading_day.strftime('%Y-%m-%d')
            return None
        except Exception:
            return None

    def _find_previous_trading_day(self, date_str: str) -> str:
        """
        找到指定日期之前的上一个交易日

        Args:
            date_str: 日期字符串 (YYYY-MM-DD)

        Returns:
            上一个交易日的字符串，如果找不到则返回None
        """
        try:
            target_date = pd.Timestamp(date_str)
            for trading_day in reversed(self.trading_days):
                if trading_day < target_date:
                    return trading_day.strftime('%Y-%m-%d')
            return None
        except Exception:
            return None

    def _generate_simulated_news_data(self, date_range: pd.DatetimeIndex, stocks: List[str]) -> Dict[str, Dict[str, List[Dict[str, Any]]]]:
        """生成模拟新闻数据"""
        news_data = {}
        
        # 为每个日期创建新闻数据
        for date in date_range:
            date_str = date.strftime('%Y-%m-%d')
            news_data[date_str] = {}
            
            # 为每只股票创建新闻
            for stock in stocks:
                # 随机决定是否有新闻
                if np.random.random() < 0.3:  # 30%概率有新闻
                    num_news = np.random.randint(1, 4)  # 1-3条新闻
                    stock_news = []
                    
                    for i in range(num_news):
                        # 生成模拟新闻
                        sentiment = np.random.choice(['positive', 'negative', 'neutral'], 
                                                  p=[0.4, 0.3, 0.3])
                        
                        # 根据情感生成标题模板
                        if sentiment == 'positive':
                            title_templates = [
                                f"{stock} Reports Strong Quarterly Results",
                                f"{stock} Announces New Product Launch",
                                f"Analysts Upgrade {stock} Stock Rating",
                                f"{stock} Expands Market Share in Key Segment"
                            ]
                        elif sentiment == 'negative':
                            title_templates = [
                                f"{stock} Misses Earnings Expectations",
                                f"Regulatory Concerns Impact {stock}",
                                f"{stock} Faces Increased Competition",
                                f"Analysts Downgrade {stock} Stock Rating"
                            ]
                        else:  # neutral
                            title_templates = [
                                f"{stock} Announces Management Changes",
                                f"{stock} to Present at Upcoming Conference",
                                f"Industry Trends and {stock}'s Position",
                                f"{stock} Maintains Market Position Despite Challenges"
                            ]
                            
                        title = np.random.choice(title_templates)
                        
                        # 创建新闻项
                        news_item = {
                            'id': f"{date_str}-{stock}-{i}",
                            'time': f"{date_str}T{np.random.randint(9, 17):02d}:{np.random.randint(0, 60):02d}:00",
                            'title': title,
                            'content': f"This is a simulated news article about {stock}. " + 
                                      f"The sentiment is {sentiment}. " +
                                      "Additional content would be included in a real implementation.",
                            'sentiment': sentiment
                        }
                        
                        stock_news.append(news_item)
                        
                    news_data[date_str][stock] = stock_news
                else:
                    news_data[date_str][stock] = []
                    
        return news_data
        
    def _load_fundamental_data(self, stocks: List[str]) -> Dict[str, Dict[str, Any]]:
        """
        加载基本面数据
        
        尝试从SQLite数据库加载，然后尝试从文件加载，如果都不存在则生成模拟数据
        """
        fundamental_data = {}
        
        # 首先尝试从SQLite数据库加载基本面数据
        for stock in stocks:
            fundamental_data[stock] = {}
            # 获取项目根目录路径
            project_root = os.path.dirname(os.path.abspath(__file__))
            db_path = os.path.join(project_root, "data", "tickers", stock, f"{stock}_data.db")
            
            if os.path.exists(db_path):
                try:
                    import sqlite3
                    import json as json_lib  # 避免与模块名冲突
                    conn = sqlite3.connect(db_path)
                    cursor = conn.cursor()
                    
                    # 检查是否存在季度财务报表和年度财务报表表
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND (name='quarterly_financials' OR name='annual_financials')")
                    if cursor.fetchone():
                        # 首先加载季度财务数据
                        cursor.execute("""
                            SELECT ticker, fiscal_date, report_type, data_json 
                            FROM quarterly_financials 
                            WHERE ticker = ? 
                            ORDER BY fiscal_date DESC
                        """, (stock,))
                        quarterly_rows = cursor.fetchall()
                        
                        # 然后加载年度财务数据
                        cursor.execute("""
                            SELECT ticker, fiscal_date, report_type, data_json 
                            FROM annual_financials 
                            WHERE ticker = ? 
                            ORDER BY fiscal_date DESC
                        """, (stock,))
                        annual_rows = cursor.fetchall()
                        
                        # 处理季度数据
                        for row in quarterly_rows:
                            _, fiscal_date, report_type, data_json = row
                            # 解析 JSON 数据
                            try:
                                data = json_lib.loads(data_json)
                                # 从日期生成季度标识
                                try:
                                    date_obj = datetime.strptime(fiscal_date, '%Y-%m-%d')
                                    quarter = (date_obj.month - 1) // 3 + 1
                                    quarter_str = f"{date_obj.year}-Q{quarter}"
                                    
                                    # 如果这个季度标识还不存在，初始化它
                                    if quarter_str not in fundamental_data[stock]:
                                        fundamental_data[stock][quarter_str] = {
                                            'fiscal_date': fiscal_date,
                                            'report_date': fiscal_date  # 用作报告日期
                                        }
                                    
                                    # 根据报表类型添加数据
                                    if report_type == 'income':
                                        fundamental_data[stock][quarter_str]['revenue'] = self._safe_float(data.get('totalRevenue', 0))
                                        fundamental_data[stock][quarter_str]['net_income'] = self._safe_float(data.get('netIncome', 0))
                                        fundamental_data[stock][quarter_str]['eps'] = self._safe_float(data.get('eps', 0))
                                    elif report_type == 'balance':
                                        fundamental_data[stock][quarter_str]['total_assets'] = self._safe_float(data.get('totalAssets', 0))
                                        fundamental_data[stock][quarter_str]['total_liabilities'] = self._safe_float(data.get('totalLiabilities', 0))
                                        # 计算债务股本比
                                        total_equity = self._safe_float(data.get('totalEquity', 0))
                                        if total_equity != 0:
                                            fundamental_data[stock][quarter_str]['debt_to_equity'] = self._safe_float(data.get('totalLiabilities', 0)) / total_equity
                                        else:
                                            fundamental_data[stock][quarter_str]['debt_to_equity'] = 0
                                    elif report_type == 'cashflow':
                                        fundamental_data[stock][quarter_str]['operating_cash_flow'] = self._safe_float(data.get('operatingCashFlow', 0))
                                        fundamental_data[stock][quarter_str]['free_cash_flow'] = self._safe_float(data.get('freeCashFlow', 0))
                                    
                                    # 计算利润率（如果有收入和净利润数据）
                                    if 'revenue' in fundamental_data[stock][quarter_str] and 'net_income' in fundamental_data[stock][quarter_str]:
                                        revenue = fundamental_data[stock][quarter_str]['revenue']
                                        if revenue != 0:
                                            fundamental_data[stock][quarter_str]['profit_margin'] = fundamental_data[stock][quarter_str]['net_income'] / revenue
                                        else:
                                            fundamental_data[stock][quarter_str]['profit_margin'] = 0
                                except Exception as e:
                                    print(f"处理季度数据时出错: {e}")
                            except json_lib.JSONDecodeError:
                                print(f"解析JSON数据失败: {data_json[:100]}...")
                        
                        # 处理年度数据，方式类似处理季度数据
                        # 只有当某个季度没有特定数据时，才使用年度数据填补
                        for row in annual_rows:
                            _, fiscal_date, report_type, data_json = row
                            try:
                                data = json_lib.loads(data_json)
                                # 从年度日期提取年份
                                try:
                                    date_obj = datetime.strptime(fiscal_date, '%Y-%m-%d')
                                    year = date_obj.year
                                    
                                    # 对每个季度检查是否已有数据，没有则从年度数据填充
                                    for q in range(1, 5):
                                        quarter_str = f"{year}-Q{q}"
                                        
                                        # 如果这个季度还没有数据，初始化它
                                        if quarter_str not in fundamental_data[stock]:
                                            fundamental_data[stock][quarter_str] = {
                                                'fiscal_date': fiscal_date,
                                                'report_date': fiscal_date,
                                                'is_annual_data': True  # 标记为年度数据
                                            }
                                        
                                        # 根据报表类型添加数据（如果该季度数据中没有对应字段）
                                        if report_type == 'income':
                                            if 'revenue' not in fundamental_data[stock][quarter_str]:
                                                fundamental_data[stock][quarter_str]['revenue'] = self._safe_float(data.get('totalRevenue', 0)) / 4  # 粗略平均到季度
                                            if 'net_income' not in fundamental_data[stock][quarter_str]:
                                                fundamental_data[stock][quarter_str]['net_income'] = self._safe_float(data.get('netIncome', 0)) / 4
                                            if 'eps' not in fundamental_data[stock][quarter_str]:
                                                fundamental_data[stock][quarter_str]['eps'] = self._safe_float(data.get('eps', 0)) / 4
                                        elif report_type == 'balance':
                                            if 'total_assets' not in fundamental_data[stock][quarter_str]:
                                                fundamental_data[stock][quarter_str]['total_assets'] = self._safe_float(data.get('totalAssets', 0))
                                            if 'total_liabilities' not in fundamental_data[stock][quarter_str]:
                                                fundamental_data[stock][quarter_str]['total_liabilities'] = self._safe_float(data.get('totalLiabilities', 0))
                                            if 'debt_to_equity' not in fundamental_data[stock][quarter_str]:
                                                total_equity = self._safe_float(data.get('totalEquity', 0))
                                                if total_equity != 0:
                                                    fundamental_data[stock][quarter_str]['debt_to_equity'] = self._safe_float(data.get('totalLiabilities', 0)) / total_equity
                                                else:
                                                    fundamental_data[stock][quarter_str]['debt_to_equity'] = 0
                                        elif report_type == 'cashflow':
                                            if 'operating_cash_flow' not in fundamental_data[stock][quarter_str]:
                                                fundamental_data[stock][quarter_str]['operating_cash_flow'] = self._safe_float(data.get('operatingCashFlow', 0)) / 4
                                            if 'free_cash_flow' not in fundamental_data[stock][quarter_str]:
                                                fundamental_data[stock][quarter_str]['free_cash_flow'] = self._safe_float(data.get('freeCashFlow', 0)) / 4
                                        
                                        # 计算利润率（如果有收入和净利润数据且尚未计算）
                                        if 'profit_margin' not in fundamental_data[stock][quarter_str] and 'revenue' in fundamental_data[stock][quarter_str] and 'net_income' in fundamental_data[stock][quarter_str]:
                                            revenue = fundamental_data[stock][quarter_str]['revenue']
                                            if revenue != 0:
                                                fundamental_data[stock][quarter_str]['profit_margin'] = fundamental_data[stock][quarter_str]['net_income'] / revenue
                                            else:
                                                fundamental_data[stock][quarter_str]['profit_margin'] = 0
                                except Exception as e:
                                    print(f"处理年度数据时出错: {e}")
                            except json_lib.JSONDecodeError:
                                print(f"解析JSON数据失败: {data_json[:100]}...")
                        
                        # 添加PE比率数据（如果价格数据可用）
                        for quarter_str, quarter_data in list(fundamental_data[stock].items()):
                            if 'eps' in quarter_data and self._safe_float(quarter_data['eps']) != 0:
                                try:
                                    # 尝试在价格数据中找到与财报日期最接近的价格
                                    report_date = quarter_data.get('fiscal_date', quarter_data.get('report_date'))
                                    if report_date and hasattr(self, 'price_data') and stock in self.price_data and not self.price_data[stock].empty:
                                        # 找到最接近报告日期的价格数据
                                        closest_price_idx = self.price_data[stock]['date'].searchsorted(report_date)
                                        if closest_price_idx < len(self.price_data[stock]):
                                            closest_price = self._safe_float(self.price_data[stock].iloc[closest_price_idx]['close'])
                                            quarter_data['pe_ratio'] = closest_price / self._safe_float(quarter_data['eps'])
                                        else:
                                            # 如果没有找到，使用最后一个价格
                                            last_price = self._safe_float(self.price_data[stock].iloc[-1]['close'])
                                            quarter_data['pe_ratio'] = last_price / self._safe_float(quarter_data['eps'])
                                except Exception as e:
                                    print(f"计算PE比率时出错: {e}")
                                    # 如果出错，使用一个合理的默认值
                                    quarter_data['pe_ratio'] = 20.0  # 设置一个合理的默认PE值
                    
                    conn.close()
                    
                    # 检查是否成功加载了数据
                    if any(fundamental_data[stock].values()):
                        print(f"从SQLite数据库 {db_path} 加载了 {stock} 的基本面数据，共 {len(fundamental_data[stock])} 个季度")
                    else:
                        print(f"从SQLite数据库 {db_path} 中没有找到 {stock} 的基本面数据")
                except Exception as e:
                    print(f"从SQLite数据库 {db_path} 加载 {stock} 的基本面数据失败: {e}")
                    import traceback
                    traceback.print_exc()
        
        # 检查是否有任何数据，如果没有则生成模拟数据
        has_real_data = any(bool(data) for data in fundamental_data.values())
        
        if not has_real_data:
            # print("未找到基本面数据，生成模拟数据") # 移除此行
            pass # 不生成模拟数据，保持为空
            
        return fundamental_data
        
    def _generate_simulated_fundamental_data(self, date_range: pd.DatetimeIndex, stocks: List[str]) -> Dict[str, Dict[str, Any]]:
        """生成模拟基本面数据"""
        fundamental_data = {}
        
        # 为每只股票创建季度财报数据
        for stock in stocks:
            fundamental_data[stock] = {}
            
            # 生成随机的基本面指标基准值
            base_revenue = np.random.uniform(1e9, 100e9)  # 10亿到1000亿美元
            base_eps = np.random.uniform(0.5, 10.0)
            base_pe = np.random.uniform(10, 40)
            
            # 为每个季度生成数据
            quarters = pd.date_range(
                start=date_range[0] - pd.DateOffset(months=12),  # 从一年前开始
                end=date_range[-1],
                freq='Q'
            )
            
            for quarter_end in quarters:
                quarter_str = quarter_end.strftime('%Y-Q%q')
                
                # 添加季度增长率变化（-5%到+10%）
                growth_rate = np.random.uniform(-0.05, 0.10)
                
                # 更新基本值
                base_revenue *= (1 + growth_rate)
                base_eps *= (1 + growth_rate)
                
                # 随机波动PE
                quarter_pe = base_pe * np.random.uniform(0.9, 1.1)
                
                fundamental_data[stock][quarter_str] = {
                    'revenue': base_revenue,
                    'eps': base_eps,
                    'pe_ratio': quarter_pe,
                    'profit_margin': np.random.uniform(0.05, 0.30),
                    'debt_to_equity': np.random.uniform(0.1, 2.0),
                    'report_date': quarter_end.strftime('%Y-%m-%d')
                }
                
        return fundamental_data
        
    def reset(self) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """
        重置交易环境
        
        返回:
            (状态, 信息)
        """
        # 初始化当前日期索引
        self.current_day_index = 0
        self.current_date = self.trading_days[self.current_day_index]
        
        # 初始化投资组合
        self.cash = self.starting_cash
        self.positions = {stock: 0 for stock in self.stocks}
        self.position_values = {stock: 0.0 for stock in self.stocks}
        
        # 计算初始净值
        self.initial_net_worth = self.starting_cash
        self.previous_net_worth = self.initial_net_worth
        
        # 初始化当前净资产
        self.net_worth = self.starting_cash
        
        # 获取当前状态
        state = self._get_state()
        
        # 初始化为未结束
        self.done = False
        
        # 创建初始信息字典
        info = {
            "net_worth": self.net_worth,
            "cash": self.cash,
            "positions": self.positions.copy(),
            "current_date": self.current_date.strftime('%Y-%m-%d') if hasattr(self.current_date, 'strftime') else str(self.current_date),
            "trading_day_index": self.current_day_index,
            "total_trading_days": self.total_days
        }
        
        # 返回初始状态和信息
        return state, info
        
    def step(self, actions: Dict[str, float]) -> Tuple[Dict[str, Any], float, bool, Dict[str, Any]]:
        """
        执行交易动作
        
        参数:
            actions: 交易动作字典，键为股票代码，值为操作比例（-1到1，负值表示卖出，正值表示买入）
            
        返回:
            (状态, 奖励, 是否结束, 信息)
        """
        # 检查是否已经结束交易
        if self.current_day_index >= self.total_days - 1:
            # 如果已经到了最后一天或超出范围，返回结束状态
            state = self.get_state()
            reward = 0.0
            done = True
            info = {
                "daily_return": 0.0,
                "net_worth": self.net_worth,
                "cash": self.cash,
                "positions": self.positions.copy(),
                "trading_completed": True,
                "reason": "到达交易结束日期"
            }
            return state, reward, done, info
        
        # 获取当前价格
        current_prices = {}
        for stock in self.stocks:
            # 确保 self.current_day_index 不会超过 price_data 的范围
            if self.current_day_index < len(self.price_data[stock]):
                current_prices[stock] = self.price_data[stock].iloc[self.current_day_index]['close']
            elif len(self.price_data[stock]) > 0: # 如果有数据，但索引越界
                # 降低警告级别，使用日志而不是print
                # print(f"警告 (step): {stock} 的 current_day_index ({self.current_day_index}) 超出价格数据范围 ({len(self.price_data[stock])}条)。使用最后已知价格。")
                current_prices[stock] = self.price_data[stock].iloc[-1]['close']
            else: # 如果根本没有价格数据
                print(f"错误 (step): {stock} 没有任何价格数据。返回0。")
                current_prices[stock] = 0 # 或者抛出更严重的错误
            
        # 执行交易
        # 首先处理特殊的全仓卖出标记
        if "__SELL_ALL__" in actions:
            print(f"  💰 执行全仓卖出操作")
            # 卖出所有持仓
            for stock in self.stocks:
                if self.positions.get(stock, 0) > 0:
                    shares_to_sell = self.positions[stock]
                    price = current_prices[stock]
                    sell_value = shares_to_sell * price
                    fee = sell_value * self.trading_fee_rate
                    self.cash += sell_value - fee
                    print(f"    📤 卖出 {stock}: {shares_to_sell} 股，价格 ${price:.2f}，获得现金 ${sell_value - fee:.2f}")
                    self.positions[stock] = 0
            # 移除特殊标记，避免后续处理
            actions = {k: v for k, v in actions.items() if k != "__SELL_ALL__"}
        
        # 处理观望操作（保持仓位不变）
        if "__HOLD__" in actions:
            print("  �� 观望操作 - 保持现有仓位不变")
            # 观望意味着本日不进行任何交易，直接移除该标记即可
            actions = {k: v for k, v in actions.items() if k != "__HOLD__"}
        
        # 处理正常的买卖操作
        for stock, action in actions.items():
            if stock not in self.stocks:
                continue
                
            price = current_prices[stock]
            
            # 全仓买入操作 (action = 1.0)
            if action > 0:
                # 使用所有现金买入该股票
                cash_to_spend = self.cash * 0.99  # 保留1%作为手续费缓冲
                if cash_to_spend > 0 and price > 0:
                    shares_to_buy = int(cash_to_spend / price)
                    if shares_to_buy > 0:
                        buy_value = shares_to_buy * price
                        fee = buy_value * self.trading_fee_rate
                        total_cost = buy_value + fee
                        
                        if total_cost <= self.cash:
                            self.cash -= total_cost
                            self.positions[stock] = self.positions.get(stock, 0) + shares_to_buy
                            print(f"    📥 全仓买入 {stock}: {shares_to_buy} 股，价格 ${price:.2f}，花费 ${total_cost:.2f}")
                        else:
                            print(f"    ⚠️ 现金不足，无法买入 {stock}")
                elif cash_to_spend > 0 and price <= 0:
                    print(f"    ⚠️ 无法买入 {stock}，价格为 ${price:.2f}")
                    
            # 全仓卖出操作 (action = -1.0)
            elif action < 0:
                shares_to_sell = self.positions.get(stock, 0)
                if shares_to_sell > 0:
                    sell_value = shares_to_sell * price
                    fee = sell_value * self.trading_fee_rate
                    self.cash += sell_value - fee
                    print(f"    📤 全仓卖出 {stock}: {shares_to_sell} 股，价格 ${price:.2f}，获得现金 ${sell_value - fee:.2f}")
                    self.positions[stock] = 0
        
        # 更新持仓价值
        for stock in self.stocks:
            self.position_values[stock] = self.positions[stock] * current_prices[stock]
            
        # 移动到下一天
        self.current_day_index += 1
        if self.current_day_index >= self.total_days:
            self.done = True
            # 保持在最后一个有效日期
            self.current_day_index = self.total_days - 1
        else:
            self.current_date = self.trading_days[self.current_day_index]
            
        # 计算新的净值和回报
        current_net_worth = self.cash + sum(self.position_values.values())
        daily_return = (current_net_worth / self.previous_net_worth) - 1
        total_return = (current_net_worth / self.initial_net_worth) - 1
        
        # 更新前一天净值和当前净资产
        self.previous_net_worth = current_net_worth
        self.net_worth = current_net_worth
        
        # 获取新状态
        state = self._get_state()
        
        # 计算奖励（日收益率）
        reward = daily_return
        
        # 返回信息
        info = {
            "net_worth": current_net_worth,
            "daily_return": daily_return,
            "total_return": total_return,
            "positions": self.positions.copy(),
            "cash": self.cash
        }
        
        return state, reward, self.done, info
        
    def _get_state(self) -> Dict[str, Any]:
        """
        获取当前环境状态
        
        返回:
            包含当前市场状态的字典
        """
        current_date_str = self.current_date.strftime('%Y-%m-%d')
        
        # 获取价格历史
        price_history = {}
        for stock in self.stocks:
            # 确保使用的索引在范围内
            # idx = min(self.current_day_index, len(self.price_data[stock]) - 1) if len(self.price_data[stock]) > 0 else 0
            # 考虑到 current_day_index 可能在 step() 结束时增加到 total_days，此时 _get_state() 获取的是下一天的开盘状态
            # 如果 current_day_index 已经是 total_days，表示模拟已结束，我们不应该尝试读取这一天的数据
            # price_history 将是到 current_day_index - 1 (即上一天收盘) 的数据

            # 获取价格历史
            # 价格历史应该是到当前 self.current_day_index 指向的那一天（但不包括）
            # 例如，如果 current_day_index = 20 (第21天)，price_window = 20, 我们需要索引 0 到 19 的数据
            start_idx = max(0, self.current_day_index - self.price_window)
            # end_idx 应该是 self.current_day_index，因为 iloc 是前闭后开
            end_idx = self.current_day_index 

            if not self.price_data[stock].empty:
                # 确保 end_idx 不超过 DataFrame 的长度
                actual_end_idx = min(end_idx, len(self.price_data[stock]))
                # 确保 start_idx 不会因为 actual_end_idx 的调整而变得无效
                actual_start_idx = min(start_idx, actual_end_idx)

                stock_price_data = self.price_data[stock].iloc[actual_start_idx:actual_end_idx]
                price_history[stock] = stock_price_data.to_dict('records')
            else:
                price_history[stock] = []
            
        # 获取新闻历史
        news_history = {}
        
        # 修复：包含当前日期和可用的历史日期
        # 首先尝试包含当前日期的新闻
        current_date_str = self.current_date.strftime('%Y-%m-%d')
        if current_date_str in self.news_data:
            news_history[current_date_str] = self.news_data[current_date_str]
        
        # 然后包含历史新闻（如果有的话）
        for i in range(1, self.news_window):
            target_day_index = self.current_day_index - i
            
            if target_day_index >= 0 and target_day_index < len(self.trading_days):
                current_news_date_obj = self.trading_days[target_day_index]
                # 将 Timestamp 对象格式化为字符串键，以匹配 self.news_data 的键结构并确保JSON兼容性
                current_news_date_str = current_news_date_obj.strftime('%Y-%m-%d') 
                if current_news_date_str in self.news_data:
                    news_history[current_news_date_str] = self.news_data[current_news_date_str]
                
        # 获取最新的基本面数据
        fundamental_history = {}
        for stock in self.stocks:
            # 找到当前日期之前的最新季度报告
            stock_fundamentals = self.fundamental_data.get(stock, {})
            latest_quarter = None
            latest_quarter_date = None
            
            for quarter, data in stock_fundamentals.items():
                quarter_date = datetime.strptime(data['report_date'], '%Y-%m-%d')
                if quarter_date <= self.current_date and (latest_quarter_date is None or quarter_date > latest_quarter_date):
                    latest_quarter = quarter
                    latest_quarter_date = quarter_date
                    
            if latest_quarter:
                fundamental_history[stock] = stock_fundamentals[latest_quarter]
            else:
                fundamental_history[stock] = {}
                
        # 构建状态字典
        state = {
            "date": current_date_str,
            "current_date": current_date_str,  # 确保智能体能正确获取日期
            "cash": self.cash,
            "positions": self.positions,
            "position_values": self.position_values,
            "price_history": price_history,
            "news_history": news_history,
            "fundamental_data": fundamental_history
        }
        
        return state 

    def _safe_float(self, value):
        try:
            return float(value)
        except (ValueError, TypeError):
            return 0.0  # 返回一个合理的默认值 
    
    def _update_net_worth(self):
        """
        更新当前净资产
        
        计算并更新 self.net_worth 属性
        """
        # 计算当前净资产 = 现金 + 所有持仓的市值
        self.net_worth = self.cash + sum(self.position_values.values())
        
    def get_state(self) -> Dict[str, Any]:
        """
        获取当前环境状态（公共方法）

        返回:
            包含当前市场状态的字典
        """
        return self._get_state()

    def is_done(self) -> bool:
        """
        检查环境是否已结束

        返回:
            如果环境已结束，则为 True，否则为 False
        """
        return self.done

    @property
    def current_net_worth(self) -> float:
        """
        获取当前净资产

        返回:
            当前净资产值
        """
        return self.cash + sum(self.position_values.values())
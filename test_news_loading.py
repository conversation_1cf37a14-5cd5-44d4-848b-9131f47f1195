#!/usr/bin/env python3
"""
测试新闻数据加载功能
"""

import sys
import os
sys.path.append('.')

from stock_trading_env import StockTradingEnv
import json

def test_news_loading():
    """测试新闻数据加载"""
    
    # 创建基本配置
    config = {
        "fail_on_large_gaps": False,
        "fill_date_gaps": False,
        "max_date_gap_days": 7
    }
    
    # 创建环境实例
    env = StockTradingEnv(config)
    
    # 测试新闻数据加载
    stocks = ['AAPL', 'MSFT']
    print(f"测试加载股票: {stocks}")
    print(f"测试日期: 2025-01-01")
    
    try:
        env.load_data('2025-01-01', '2025-01-01', stocks)
        
        # 检查新闻数据
        print('\n=== 新闻数据加载结果 ===')
        total_news = 0
        
        for date_str, stocks_news in env.news_data.items():
            print(f'\n日期: {date_str}')
            for stock, news_list in stocks_news.items():
                print(f'  {stock}: {len(news_list)} 条新闻')
                total_news += len(news_list)
                
                # 显示前3条新闻的标题
                for i, news_item in enumerate(news_list[:3]):
                    title = news_item.get('title', '无标题')
                    sentiment = news_item.get('sentiment', '未知')
                    print(f'    [{i+1}] {title[:80]}... (情绪: {sentiment})')
        
        print(f'\n总计加载新闻: {total_news} 条')
        
        if total_news > 0:
            print("✅ 新闻数据加载成功！")
        else:
            print("❌ 未加载到任何新闻数据")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_news_loading()

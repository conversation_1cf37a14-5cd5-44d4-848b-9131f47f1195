#!/usr/bin/env python3
"""
检查NAA代理的最新输出
"""

import json
import os
from datetime import datetime

def check_naa_latest():
    """检查NAA代理的最新输出"""

    # 检查最新的NAA记录 - 从最近运行的系统中
    naa_outputs_file = 'data/trading/2025-01-31/NAA/outputs.json'
    
    if os.path.exists(naa_outputs_file):
        with open(naa_outputs_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f'NAA输出记录总数: {len(data)}')
        
        if data:
            latest = data[-1]
            print(f'最新记录时间: {latest.get("timestamp")}')
            print(f'LLM使用: {latest.get("llm_used")}')
            
            if 'raw_response' in latest:
                response = latest['raw_response']
                print(f'情绪评分: {response.get("sentiment")}')
                summary = response.get("summary", "")
                print(f'摘要: {summary[:100]}...')
                
                # 检查是否有关键事件
                if 'key_events' in response:
                    events = response['key_events']
                    print(f'关键事件数量: {len(events)}')
                    for i, event in enumerate(events[:3]):
                        print(f'  事件{i+1}: {event.get("event", "未知事件")}')
            
            print(f'处理时间: {latest.get("processing_time", 0):.2f}秒')
            print(f'信心度: {latest.get("confidence", "未知")}')
        else:
            print('没有输出记录')
    else:
        print('NAA输出文件不存在')
    
    # 检查输入文件
    naa_inputs_file = 'data/trading/2025-01-31/NAA/inputs.json'
    if os.path.exists(naa_inputs_file):
        with open(naa_inputs_file, 'r', encoding='utf-8') as f:
            input_data = json.load(f)
        
        print(f'\nNAA输入记录总数: {len(input_data)}')
        
        if input_data:
            latest_input = input_data[-1]
            state_data = latest_input.get('state_data', {})
            news_history = state_data.get('news_history', {})
            
            print(f'最新输入时间: {latest_input.get("timestamp")}')
            print(f'新闻历史数据: {len(news_history)} 个日期')
            
            total_news = 0
            for date_str, stocks_news in news_history.items():
                date_news_count = sum(len(news_list) for news_list in stocks_news.values())
                total_news += date_news_count
                print(f'  {date_str}: {date_news_count} 条新闻')
            
            print(f'总新闻数量: {total_news}')

if __name__ == "__main__":
    check_naa_latest()
